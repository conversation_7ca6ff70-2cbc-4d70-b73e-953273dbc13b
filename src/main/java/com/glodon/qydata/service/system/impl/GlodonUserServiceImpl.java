package com.glodon.qydata.service.system.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.glodon.qydata.common.AccountTypeService;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.enums.AccountTypeEnum;
import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.config.CommonConfig;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.entity.system.ZbLibUser;
import com.glodon.qydata.service.cloud.CloudAccountFeignService;
import com.glodon.qydata.service.cloud.CloudFeignService;
import com.glodon.qydata.service.cloud.CloudService;
import com.glodon.qydata.service.cloud.GlodonService;
import com.glodon.qydata.service.depend.DigitalCostService;
import com.glodon.qydata.service.system.*;
import com.glodon.qydata.util.BasicAuthBuilder;
import com.glodon.qydata.util.DataDealUtil;
import com.glodon.qydata.util.RedisUtil;
import com.glodon.qydata.vo.cloud.CloudR;
import com.glodon.qydata.vo.system.EntInfoVo;
import com.glodon.qydata.vo.cloud.UserInfo;
import com.glodon.qydata.entity.system.QYFlag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.List;

/**
 * Created by weijf on 2021/12/22.
 */
@Slf4j
@Service
public class GlodonUserServiceImpl implements IGlodonUserService {

    @Autowired
    private GlodonService glodonService;
    @Autowired
    private CloudFeignService cloudFeignService;
    @Autowired
    private CloudService cloudService;
    @Autowired
    private ZbLibUserService zbLibUserService;

    @Autowired
    private CommonConfig commonConfig;
    @Autowired
    private CloudAccountFeignService cloudAccountFeignService;
    @Autowired
    private DigitalCostService digitalCostService;
    @Autowired
    private RedisUtil redisUtil;
    @Resource
    private AccountTypeService accountTypeService;

    /**
     * 查询企业下的用户列表
     * @param globalId
     * @param identity
     * @return
     * @throws BusinessException
     * <AUTHOR>
     */
    @Override
    public JSONArray getEntMemberList(String globalId, String identity) throws BusinessException {
        JSONArray array = new JSONArray();
        if(StringUtils.isEmpty(globalId)){
            return array;
        }
        String accessToken = glodonService.getClientCredentialsAccessToken();
        //先查询企业ID
        String mainGlobalId = getEnterpriseId(globalId,accessToken);

        boolean isLastPage = true;
        int pageNum = 1;
        do {
            JSONObject resultBody = glodonService.getEntMemberList(accessToken,mainGlobalId,identity,pageNum,"200","0");
            int code = resultBody == null ? -1 : resultBody.getIntValue("code");
            if (0  == code) {
                JSONObject data = resultBody.getJSONObject("data");
                isLastPage = data.getBoolean("isLastPage");
                JSONArray list = data.getJSONArray("list");
                if(list!=null && !list.isEmpty()) {
                    array.addAll(list);
                }else{
                    break;
                }
            }
            pageNum ++;
        }while(!isLastPage);

        return array;
    }

    /**
     * 查询企业ID
     * @param globalId
     * @param accessToken
     * @return
     * <AUTHOR>
     */
    @Override
    public String getEnterpriseId(String globalId,String accessToken){
        JSONObject entInfo;
        try {
            entInfo = getEntInforFromCached(globalId,accessToken);
        } catch (BusinessException e) {
            return null;
        }
        String mainGlobalId = null;
        if (entInfo!=null){
            JSONObject enterprise = entInfo.getJSONObject("enterprise");
            if (enterprise!=null){
                mainGlobalId = enterprise.getLong("id")==null?null:enterprise.getString("id");
            }
        }

        if (StringUtils.isBlank(mainGlobalId)){
            return null;
        }

        return mainGlobalId;
    }

    /**
     * 根据globalId从广材网查询企业信息
     * @param globalId
     * @return
     * <AUTHOR>
     */
    @Override
    public EntInfoVo getEntInfo(String globalId) {
        CloudR<EntInfoVo> result = null;
        EntInfoVo entInfo = null;
        try {
            String gcwCloudToken = cloudService.getCacheToken();
            result = cloudFeignService.getEntInfo(globalId, commonConfig.GCW_APPID, gcwCloudToken);
        } catch (Exception e) { //再查一次
            if (e.getMessage().contains("402")) {
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e1) {
                    Thread.currentThread().interrupt();
                    log.error("睡眠500ms执行失败");
                }

                String newCloudToken = cloudService.getAndCacheToken();
                result = cloudFeignService.getEntInfo(globalId, commonConfig.GCW_APPID, newCloudToken);
            }
        }
        if (result != null) {
            entInfo = result.getObject();
        }
        return entInfo;
    }

    /**
     * 查询企业信息，先查询缓存
     * @param globalId
     * @param accessToken
     * @return
     * @throws BusinessException
     * <AUTHOR>
     */
    @Override
    public JSONObject getEntInforFromCached(String globalId, String accessToken) throws BusinessException {
        JSONObject data  = redisUtil.getObject(RedisKeyEnum.ENT_INFO_BY_ID,JSONObject.class,globalId);
        if(data != null && !data.isEmpty()){
            return data;
        }

        if (StringUtils.isBlank(accessToken)) {
            accessToken = glodonService.getClientCredentialsAccessToken();
        }
        data = glodonService.getEntInfor(globalId, accessToken);

        if(data != null && !data.isEmpty()){
            redisUtil.setObject(RedisKeyEnum.ENT_INFO_BY_ID,data,globalId);
        }
        return data;
    }

    @Override
    public List<String> getUserIdsByGlobalIds(List<String> globalIds) {
        return glodonService.getUserIdsByGlobalIds(globalIds);
    }

    @Override
    public String getAccountNameById(String userId, String sgToken) {
       if (StringUtils.isEmpty(userId)) {
           log.error("用户id为空");
           return null;
       }
       String accoutName = redisUtil.getString(RedisKeyEnum.MAIN_ACCOUNT_NAME, userId);
       if (StringUtils.isNotBlank(accoutName)) {
           return accoutName;
       }
       accoutName = digitalCostService.getAccountNameById(userId, sgToken);
       if (!StringUtils.isEmpty(accoutName)) {
           redisUtil.setString(RedisKeyEnum.MAIN_ACCOUNT_NAME, accoutName, userId);
       }
       return accoutName;
    }

    @Override
    public JSONArray getUserInfoByUserIds(List<String> userIds) {
        return glodonService.getUserInfoByUserIds(userIds);
    }

    /**
     * 根据globalId查询CustomerCode
     * @param globalId
     * @return
     * @throws BusinessException
     * @auther: niecw-a
     */
    @Override
    public String getCustomerCode(String globalId) throws BusinessException {
        String customerCode = getCustomerCodeInner(globalId);
        String enterpriseId = getEnterpriseId(globalId, null);
        if ("-1".equals(customerCode) || "-100".equals(customerCode) || "-1".equals(enterpriseId) || "-100".equals(enterpriseId)) {
            throw new BusinessException("企业标识获取错误，请稍后再试");
        }
        // 确定使用企业id还是企业编码
        String cc = setQyFlagCache(customerCode, enterpriseId);
        // 添加企业账号和个人账号的判断 然后进行globalId和类型的缓存,判断是否需要进行值的替换
        AccountTypeEnum accountType = accountTypeService.getAccountType(globalId);
        // 当前账号为个人账号并且当前访问的接口路径为openapi且不包含修改路径
        if (AccountTypeEnum.PERSONAL_TYPE.equals(accountType)) {
            log.info("当前登录的人为个人账号,global_id为:[{}]", globalId);
            return Constants.PERSONAL_CUSTOMER_CODE;
        }
        return cc;
    }

    /**
     * 设置企业标识缓存, 确定使用企业id还是企业编码
     * @param customerCode
     * @param enterpriseId
     * @return
     * <AUTHOR>
     */
    @Override
    public String setQyFlagCache(String customerCode, String enterpriseId) {
        boolean isCode = StringUtils.isBlank(enterpriseId) || zbLibUserService.isEntCustomerDup(customerCode, enterpriseId);
        String key = isCode ? customerCode : enterpriseId;

        QYFlag qyFlagVo = new QYFlag();
        qyFlagVo.setQyFlag(isCode ? 0 : 1);
        qyFlagVo.setQyCodeOld(customerCode);
        redisUtil.setObject(RedisKeyEnum.QY_FLAG_INFO, qyFlagVo, key);
        return key;
    }

    /**
     * 根据globalId查询CustomerCode（内部方法）
     * @param globalId
     * @return
     * @throws BusinessException
     * @auther: weijf
     */
    public String getCustomerCodeInner(String globalId)throws BusinessException {
        DataDealUtil.assertGlobalIdIsValid(globalId);

        String customerCode = null;
        String function = null;
        try {
            // 放置redis缓存
            customerCode = redisUtil.getString(RedisKeyEnum.CUSTOMER_CODE, globalId);
            if (customerCode != null) {
                return  customerCode;
            }
            //先查本地库
            ZbLibUser libUser = zbLibUserService.getUserByGlobalId(globalId);

            if (libUser !=null) {
                customerCode = StringUtils.isBlank(libUser.getCustomerCode())?(libUser.getAuthEnterpriseId()==null?"":libUser.getAuthEnterpriseId().toString()): libUser.getCustomerCode();
            }

            if(StringUtils.isNotBlank(customerCode)){

                // 是否查zb_lib_user表；zb_lib_user表里的customerCode到底带不带独立库后缀；目前还不明了
                // 短期处理，截取独立库后缀
                if (customerCode.length() - customerCode.lastIndexOf("_") == 33){
                    customerCode = customerCode.substring(0, customerCode.length() - 33);
                }
                redisUtil.setString(RedisKeyEnum.CUSTOMER_CODE, customerCode, globalId);
                return customerCode;
            }

            //  根据EnterpriseId在库中查同企业数据
            String accessToken = glodonService.getClientCredentialsAccessToken();
            String enterpriseId = getEnterpriseId(globalId, accessToken);
            if(StringUtils.isBlank(enterpriseId)){
                log.warn("---根据globalId= {} 查询 enterpriseId 未查询到相关数据---", globalId);
            } else {
                customerCode = zbLibUserService.getCustomerCodeByEnterpriseId(enterpriseId);
            }
            if (StringUtils.isNotBlank(customerCode)) {
                return  customerCode;
            }

            //第二次 若为空,则从广材网查企业信息
            EntInfoVo entInfo = getEntInfo(globalId);

            if(entInfo != null){
                customerCode = entInfo.getCustomerCode();
            }

            //第三次 从广联云查EnterpriseId
            // 获取不到企业编码，用企业id代替企业编码，兼容建设方BU打通没有企业编码的情况，并且把企业id到编码的映射入库，后续该企业均用这个企业标识
            if (StringUtils.isEmpty(customerCode) && StringUtils.isNotEmpty(enterpriseId)) {
                String customerCodeInDup = getCustomerCodeInSysZbCustomerDup(enterpriseId);

                customerCode = StringUtils.isBlank(customerCodeInDup) ? enterpriseId : customerCodeInDup;
                if(libUser == null){
                    libUser = new ZbLibUser();
                }
                libUser.setGlobalId(globalId);
                libUser.setCustomerCode(customerCode);
                libUser.setAuthEnterpriseId(Long.parseLong(enterpriseId));
                if ((!"-1".equals(customerCode)) && (!"-100".equals(customerCode)) && StringUtils.isBlank(customerCodeInDup)) {
                    zbLibUserService.syncUser(libUser);
                }
            }

            //三次均为空：个人帐号，无企业编码
            if (StringUtils.isEmpty(customerCode)) {
                customerCode = globalId;  //用globalId代替customerCode
            }
        }catch (Exception e){
            if(function == null){
                StackTraceElement[] stacks = (new Throwable()).getStackTrace();
                function = stacks[3].getMethodName()+ " - " + stacks[2].getMethodName()+ " - " + stacks[1].getMethodName();
            }
            log.error("根据globalId查询CustomerCode error,e={},globalId={},function={}",e.getMessage(),globalId,function);
        }
        if ((!"-1".equals(customerCode)) && (!"-100".equals(customerCode))) {
            redisUtil.setString(RedisKeyEnum.CUSTOMER_CODE, customerCode, globalId);
        }
        return customerCode;

    }

    private String getCustomerCodeInSysZbCustomerDup(String enterpriseId) {
        return zbLibUserService.getCustomerCodeInSysZbCustomerDup(enterpriseId);
    }

    /**
     * 获取用户名
     * @param globalId
     * @param accessToken
     * @return
     * <AUTHOR>
     */
    @Override
    public String getUserName(String globalId,String accessToken){
//        String cacheUserName = redisUtil.getString(RedisKeyEnum.ENT_USER_NAME, globalId);
//        if (StringUtils.isNotBlank(cacheUserName)) {
//            return cacheUserName;
//        }
        String userName = null;
        if(StringUtils.isEmpty(accessToken)) {
            accessToken = glodonService.getClientCredentialsAccessToken();
        }
//        try {
//            //从广联云获取
//            UserInfo user = cloudAccountFeignService.getUser(globalId, BasicAuthBuilder.headerValue());
//            if (user != null) {
//                userName = user.getUsername();
//            }
//        }catch (Exception e){ //处理删除账号
//            String deleteAccountName = getDeleteAccountName(globalId,accessToken);
//            userName = deleteAccountName + "（该用户已被删除）";
//        }
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        String sgToken = requestAttributes.getRequest().getHeader(Constants.HEADER_SG_TOKEN);
        userName = digitalCostService.getAccountNameById(globalId, sgToken);

        //仍为空，再从广联云企业获取
        if (userName == null || !userName.contains("删除")) {
            String newUserName = getEntMemberName(globalId,accessToken);
            //组装用户名称
            if(StringUtils.isNotEmpty(newUserName)){
                if (StringUtils.isNotBlank(userName) && !newUserName.contains(userName)) {
                    userName = newUserName + "（" + userName + "）";
                } else {
                    userName = newUserName;
                }
            }
        }
        //redisUtil.setString(RedisKeyEnum.ENT_USER_NAME, userName, globalId);
        return userName;
    }

    /**
     * 查询已删除的用户
     * @param globalId
     * @param accessToken
     * @return
     * <AUTHOR>
     */
    private String getDeleteAccountName(String globalId, String accessToken) {
        try{
            //先从缓存查
            String oldUserName = redisUtil.getString(RedisKeyEnum.DELETED_ACCOUNT_NAME,globalId);
            if (StringUtils.isNotEmpty(oldUserName)){
                return oldUserName;
            }

            //再从数据库查
            ZbLibUser libUser = zbLibUserService.getUserByGlobalId(globalId);
            if (libUser != null) {
                return libUser.getFullName()==null?libUser.getAccountName():libUser.getFullName();
            }

            //还没查到就从广联云查
            String enterpriseId = getEnterpriseId(globalId, accessToken);
            JSONObject result = glodonService.getEntMemberList(accessToken, enterpriseId, null, 1, "30", "1");
            if (result != null && !result.isEmpty()) {
                JSONObject data = result.getJSONObject("data");
                if (data != null && !data.isEmpty()) {
                    JSONArray accountArray = data.getJSONArray("list");
                    if (!CollectionUtils.isEmpty(accountArray)) {
                        for (int i = 0; i < accountArray.size(); i++) {
                            JSONObject deletedAccount = accountArray.getJSONObject(i);
                            String deleteGlobalId = deletedAccount.getString("globalId");
                            String deleteAccountName = deletedAccount.getString("name");
                            if (StringUtils.isNotEmpty(deleteGlobalId) && deleteGlobalId.equals(globalId) && StringUtils.isNotEmpty(deleteAccountName)) {
                                //存入缓存
                                redisUtil.setString(RedisKeyEnum.DELETED_ACCOUNT_NAME,deleteAccountName,globalId);
                                return deleteAccountName;
                            }
                        }
                    }
                }
            }

        }catch (Exception e){
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 单个查询企业下用户信息
     * @param globalId
     * @param accessToken
     * @return
     * <AUTHOR>
     */
    private String getEntMemberName(String globalId, String accessToken) {
        String entName = null;
        if(StringUtils.isEmpty(globalId)){
            return entName;
        }
        //从缓存获取
        entName = redisUtil.getString(RedisKeyEnum.ENT_MEMBER_NAME,globalId);
        if(StringUtils.isNotEmpty(entName)){
            return entName;
        }

        try {
            JSONObject data = glodonService.getEntMember(accessToken, globalId);
            if (data != null && !data.isEmpty()) {
                entName = data.getString("name");
            }
        }catch (BusinessException e){
            if(e.getMessage().contains("No permission")){ //token失效，删除重新获取
                redisUtil.delete(RedisKeyEnum.ACCESS_TOKEN_CLIENT_CREDENTIALS);
                accessToken = glodonService.getClientCredentialsAccessToken();
                try {
                    JSONObject data = glodonService.getEntMember(accessToken, globalId);
                    if (data != null && !data.isEmpty()) {
                        entName = data.getString("name");
                    }
                }catch (Exception e2){
                    log.error("查询成员信息接口出错");
                }
            }
        }

        //放入缓存
        if(StringUtils.isNotEmpty(entName)){
            redisUtil.setString(RedisKeyEnum.ENT_MEMBER_NAME,entName,globalId);
        }

        return entName;
    }
}
