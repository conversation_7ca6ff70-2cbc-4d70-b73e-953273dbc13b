package com.glodon.qydata.service.standard.expression;

import com.glodon.qydata.dto.ExpressionItem;
import com.glodon.qydata.entity.standard.expression.ZbStandardsExpression;
import com.glodon.qydata.vo.standard.expression.ExpressionResultVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 工程特征名称类型表及计算口径
 * @date 2021/11/2 17:13
 */
public interface IExpressionService {

    /**
     *  根据主键查询
     * @param id
     * @return com.gcj.zblib.standardData.expression.entity.ZbStandardsExpression
     * <AUTHOR>
     * @date 2021/11/5 8:51
     */
    ZbStandardsExpression selectByPrimaryKey(Long id);

    /**
     *  按企业查询
     * @param customerCode
     * @return java.util.List<com.gcj.zblib.standardData.expression.entity.ZbStandardsExpression>
     * <AUTHOR>
     * @date 2021/11/5 8:51
     */
    List<ZbStandardsExpression> selectListByCustomCode(String customerCode, Integer type);

    /**
     *  获取计算口径列表
     * @param customerCode
     * @return java.util.List<com.gcj.zblib.standardData.expression.entity.ZbStandardsExpression>
     * <AUTHOR>
     * @date 2021/11/5 8:51
     */
    List<ExpressionResultVo> selectExpression(String customerCode, Integer type, Integer isSkipUserName);

    /**
     * 获取有效的计算口径列表
     * @param customerCode
     * @param type
     * @param isSkipUserName
     * @return
     */
    List<ExpressionResultVo> selectAllExpression(String customerCode, Integer type, Integer isSkipUserName);

    void expressionSyn(String customerCode, Integer type);

    /**
     * 批量添加计算口径
     * @param customerCode
     * @param expressionItems
     * @return
     */
    Map<String, ZbStandardsExpression> batchAddExpressions(String customerCode, List<ExpressionItem> expressionItems, Integer type);
}
