package com.glodon.qydata.service.standard.expression.impl;

import com.glodon.qydata.common.RequestContent;
import com.glodon.qydata.common.annotation.BusinessCache;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.enums.ExpressionTypeEnum;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.dto.ExpressionItem;
import com.glodon.qydata.entity.standard.expression.ZbStandardsExpression;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.standard.expression.ZbStandardsExpressionMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureMapper;
import com.glodon.qydata.mapper.standard.projectOrContractInfo.StandardsProjectInfoMapper;
import com.glodon.qydata.service.init.enterprise.InitTradeService;
import com.glodon.qydata.service.standard.expression.IExpressionService;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import com.glodon.qydata.vo.standard.expression.ExpressionResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.glodon.qydata.common.constant.Constants.WHETHER_FALSE;

/**
 * <AUTHOR>
 * @description: 工程特征名称类型表及计算口径
 * @date 2021/11/2 17:15
 */
@Service
@Slf4j
public class ExpressionServiceImpl implements IExpressionService {

    @Autowired
    private ZbStandardsExpressionMapper zbStandardsExpressionMapper;

    @Autowired
    private InitTradeService initTradeService;

    @Autowired
    private StandardsProjectInfoMapper standardsProjectInfoMapper;

    @Autowired
    private ProjectFeatureMapper projectFeatureMapper;

    @Override
    public ZbStandardsExpression selectByPrimaryKey(Long id) {
        ZbStandardsExpression zbStandardsExpression = zbStandardsExpressionMapper.selectByPrimaryKey(id);
        if (Objects.isNull(zbStandardsExpression)){
            throw new BusinessException(ResponseCode.ERROR, "该数据不存在");
        }
        return zbStandardsExpression;
    }

    @Override
    public List<ZbStandardsExpression> selectListByCustomCode(String customerCode, Integer type) {
        return zbStandardsExpressionMapper.selectListByCustomCode(customerCode, type);
    }

    @Override
    @BusinessCache(customerCode = "${customerCode}")
    public List<ExpressionResultVo> selectExpression(String customerCode, Integer type, Integer isSkipUserName) {
        List<ExpressionResultVo> list = new ArrayList<>();
        List<ZbStandardsExpression> zbStandardsExpressions = zbStandardsExpressionMapper.selectExpression(customerCode, type);

        zbStandardsExpressions = getZbStandardsExpressions(customerCode, type, zbStandardsExpressions);

        convertExpressions(list, zbStandardsExpressions, isSkipUserName);
        return list;
    }

    @Override
    public List<ExpressionResultVo> selectAllExpression(String customerCode, Integer type, Integer isSkipUserName) {
        List<ExpressionResultVo> list = new ArrayList<>();
        List<ZbStandardsExpression> zbStandardsExpressions = zbStandardsExpressionMapper.selectAllExpression(customerCode, type);

        zbStandardsExpressions = getZbStandardsExpressions(customerCode, type, zbStandardsExpressions);

        convertExpressions(list, zbStandardsExpressions, isSkipUserName);
        return list;
    }

    private List<ZbStandardsExpression> getZbStandardsExpressions(String customerCode, Integer type, List<ZbStandardsExpression> zbStandardsExpressions) {
        if (CollectionUtils.isEmpty(zbStandardsExpressions)){
            initTradeService.initData(customerCode);
            zbStandardsExpressions = zbStandardsExpressionMapper.selectExpression(customerCode, type);
        }
        return zbStandardsExpressions;
    }
    private void convertExpressions(List<ExpressionResultVo> list, List<ZbStandardsExpression> zbStandardsExpressions, Integer isSkipUserName) {
        //获取某个企业人员的globalId和姓名的映射关系
        Map<String, String> globalIdNameMap = new HashMap<>();
        if (WHETHER_FALSE.equals(isSkipUserName)){
            globalIdNameMap = RequestContent.getGlobalIdNameMap(zbStandardsExpressions.stream().map(expr -> String.valueOf(expr.getExpressionCreateGlobalId())).distinct().collect(Collectors.toList()));
        }
        for (ZbStandardsExpression zbStandardsExpression : zbStandardsExpressions) {
            ExpressionResultVo expressionResultVo = new ExpressionResultVo();
            BeanUtils.copyProperties(zbStandardsExpression, expressionResultVo);
            Long createGlobalId = zbStandardsExpression.getExpressionCreateGlobalId();
            if (Objects.nonNull(createGlobalId) && globalIdNameMap.containsKey(createGlobalId.toString())) {
                expressionResultVo.setExpressionCreateGlobalName(globalIdNameMap.get(createGlobalId.toString()));
            }
            list.add(expressionResultVo);
        }
    }

    // 有一批内置转成非内置的计算口径，按内置的规则处理，总在字典表里，不删除
    public static final List<String> ORIGINAL_SYSTEM = Arrays.asList("楼层总数(m2)", "售楼处硬装面积(m2)", "示范样板房面积(m2)",
            "户均建筑面积(m2)", "实体样板间精装总面积(m2)", "装配式建筑面积(m2)", "驳坎长度(m)", "实体样板间面积(m2)", "基坑支护长度(m)");

    @Override
    public void expressionSyn(String customerCode, Integer type){
        List<ZbStandardsExpression> expressionList = new ArrayList<>();

        // 查询工程特征表的计算口径
        List<ZbStandardsExpression> featureExpressionList = projectFeatureMapper.selectExpression(customerCode, type);

        if (CollectionUtils.isNotEmpty(featureExpressionList)){
            expressionList.addAll(featureExpressionList);
        }

        // 查询项目信息表的计算口径
        List<ZbStandardsExpression> projectExpressionList = standardsProjectInfoMapper.selectExpression(customerCode);

        if (CollectionUtils.isNotEmpty(projectExpressionList)){
            expressionList.addAll(projectExpressionList);
        }

        // 排序，单位取最新的
        List<ZbStandardsExpression> sortList = expressionList.stream().sorted(Comparator.comparing(ZbStandardsExpression::getUpdateTime, Comparator.nullsFirst(Comparator.naturalOrder()))).collect(Collectors.toList());

        batchAddOrUpdateExpressions(customerCode, type, sortList);
    }

    private void batchAddOrUpdateExpressions(String customerCode, Integer type, List<ZbStandardsExpression> sortList) {
        List<ZbStandardsExpression> numberExpressionList = zbStandardsExpressionMapper.selectListByCustomCode(customerCode, type);

        numberExpressionList.stream().filter(x -> !Constants.WHETHER_TRUE.equals(x.getExpressionIsFromSystem()) && !ORIGINAL_SYSTEM.contains(x.getOldName())).forEach(item -> {
            item.setIsExpression(Constants.WHETHER_FALSE);
            item.setUnit("");
        });

        Map<String, ZbStandardsExpression> nameMap = numberExpressionList.stream().collect(Collectors.toMap(ZbStandardsExpression::getName, Function.identity(), (v1, v2) -> v2));

        int maxOrd = numberExpressionList.stream()
                .map(ZbStandardsExpression::getExpressionOrd)
                .filter(Objects::nonNull).max(Comparator.comparingInt(o -> o)).orElse(0);

        Map<String, ZbStandardsExpression> insertMap = new HashMap<>();

        for (ZbStandardsExpression sortExpression : sortList) {
            String name = sortExpression.getName();
            if (nameMap.containsKey(name)){
                ZbStandardsExpression expression = nameMap.get(name);
                expression.setIsExpression(Constants.WHETHER_TRUE);
                expression.setUnit(sortExpression.getUnit());
                expression.setIsDeleted(Constants.WHETHER_FALSE);
                expression.setExpressionIsUsing(Constants.WHETHER_TRUE);
            }else {
                if (insertMap.containsKey(name)){
                    ZbStandardsExpression expression = insertMap.get(name);
                    expression.setUnit(sortExpression.getUnit());
                }else {
                    ZbStandardsExpression expression = createExpressionItem(name, sortExpression.getUnit(), customerCode, type, ++maxOrd);
                    insertMap.put(name, expression);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(insertMap.values())){
            List<ZbStandardsExpression> insertList = new ArrayList<>(insertMap.values());
            zbStandardsExpressionMapper.batchInsert(insertList);
        }

        if (CollectionUtils.isNotEmpty(numberExpressionList)){
            zbStandardsExpressionMapper.batchUpdateSelective(numberExpressionList);
        }
    }

    /**
     * 生成一条计算口径记录
     * @param name: 计算口径名
     * @param unit: 计算口径单位
     * @param customerCode: 企业编码
     * @param type: 类型
     * @param ord: 顺序
     * @return
     */
    private ZbStandardsExpression createExpressionItem(String name, String unit, String customerCode, Integer type, Integer ord){
        ZbStandardsExpression expression = new ZbStandardsExpression();
        expression.setId(SnowflakeIdUtils.getNextId());
        expression.setExpressionCode("K_"+ SnowflakeIdUtils.getNextId());
        expression.setName(name);
        expression.setTypeCode(ExpressionTypeEnum.TYPE_NUMBER.getCode());
        expression.setIsExpression(Constants.WHETHER_TRUE);
        expression.setUnit(unit);
        expression.setIsDeleted(Constants.WHETHER_FALSE);
        expression.setQyCode(customerCode);
        expression.setType(type);
        expression.setExpressionIsUsing(Constants.WHETHER_TRUE);
        expression.setExpressionOrd(ord);
        expression.setIsUsable(Constants.WHETHER_FALSE);
        expression.setExpressionIsFromSystem(Constants.WHETHER_FALSE);
        return expression;
    }

    @Override
    public Map<String, ZbStandardsExpression> batchAddExpressions(String customerCode, List<ExpressionItem> expressionItems, Integer type) {
        List<ZbStandardsExpression> expressions = new ArrayList<>();
        for (ExpressionItem item : expressionItems) {
            ZbStandardsExpression expression = new ZbStandardsExpression();
            expression.setName(item.getName());
            expression.setUnit(item.getUnit());
            expression.setType(type);
            expressions.add(expression);
        }

        batchAddOrUpdateExpressions(customerCode, type, expressions);
        List<ZbStandardsExpression> numberExpressionList = zbStandardsExpressionMapper.selectListByCustomCode(customerCode, type);
        return numberExpressionList.stream().collect(Collectors.toMap(ZbStandardsExpression::getName, Function.identity(), (v1, v2) -> v2));

    }
}
