package com.glodon.qydata.controller.standard.feature;

import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.common.annotation.Permission;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.constant.OperateConstants;
import com.glodon.qydata.dto.BatchAddExpressionDto;
import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.service.standard.category.CommonProjCategoryService;
import com.glodon.qydata.service.standard.category.impl.CommonProjectCategoryUsedService;
import com.glodon.qydata.service.standard.feature.IProjectFeatureSelfService;
import com.glodon.qydata.service.standard.feature.IProjectFeatureService;
import com.glodon.qydata.util.CategoryUtil;
import com.glodon.qydata.util.PublishLockerUtil;
import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.vo.standard.feature.*;
import com.glodon.qydata.vo.standard.trade.StandardTradeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Objects;

import static com.glodon.qydata.common.constant.Constants.ZbFeatureConstants.WHETHER_FALSE;

/**
 * @description: 工程特征控制层
 * <AUTHOR>
 * @date 2021/10/21 17:59
 */
@RestController
@RequestMapping("/basicInfo/standards/zbFeature")
@Slf4j
@Tag(name = "工程特征相关接口类", description = "工程特征相关接口类")
public class ProjectFeatureController extends BaseController {

    @Autowired
    private IProjectFeatureService projectFeatureService;

    @Autowired
    private IProjectFeatureSelfService projectFeatureSelfService;

    @Autowired
    private PublishLockerUtil publishLockerUtil;


    @Autowired
    private CommonProjectCategoryUsedService commonProjectCategoryUsedService;
    @Autowired
    private CommonProjCategoryService commonProjCategoryService;

//------------------------------------------------------以下是企业级别-------------------------------------------------------------------
    /**
     * @description: 获取特征列表（分类视图、专业视图）--企业级
     * @param filterVO
     * @return  ResponseVo
     * <AUTHOR>
     * @date 2021/10/25 20:09
     */
    @Operation(summary = "获取特征列表（分类视图、专业视图）--企业级")
    @GetMapping
    public ResponseVo featureList(ProjectFeatureFilterVO filterVO){
        String customerCode = getCustomerCode();
        filterVO.setIsSkipUserName(WHETHER_FALSE);

        if (Constants.ZbFeatureConstants.ViewType.TRADE_VIEW.equals(filterVO.getViewType()) && Objects.nonNull(filterVO.getTradeId())){
            // 专业视图
            List<ProjectFeatureResultVO> projectFeatureResultVOS = projectFeatureService.featureTradeView(customerCode, filterVO);
            return ResponseVo.success(projectFeatureResultVOS);
        }

        if (Constants.ZbFeatureConstants.ViewType.CATEGORY_VIEW.equals(filterVO.getViewType()) && StringUtils.isNotEmpty(filterVO.getCategoryCode())){
            // 分类视图   查询某企业的某个分类的特征。根据专业排序显示
            filterVO.setIsShowNotUsing(Constants.ZbFeatureConstants.WHETHER_TRUE);
            List<ProjectFeatureCategoryViewVO> projectFeatureCategoryViewVOS = projectFeatureService.featureCategoryView(customerCode, filterVO);
            return ResponseVo.success(projectFeatureCategoryViewVOS);
        }

        return ResponseVo.error();
    }

    /**
     * @description: 根据一级工程分类获取-特征的工程专业列表(指标库-数据分析)--企业级
     * @param categoryCode
     * @return com.glodon.qydata.vo.common.ResponseVo<com.glodon.qydata.vo.standard.trade.StandardTradeVO>
     * <AUTHOR>
     * @date 2022/1/17 19:26
     */
    @Operation(summary = "根据一级工程分类获取-特征的工程专业列表(指标库-数据分析)--企业级")
    @GetMapping("/getTradeByCategoryFeature")
    public ResponseVo<List<StandardTradeVO>> getTradeByCategoryFeature(String categoryCode) {
        return ResponseVo.success(projectFeatureService.getTradeByCategoryFeature(categoryCode));
    }

    /**
     * @description: 根据一级工程分类 和 工程专业 获取工程特征(指标库-数据分析)--企业级
     * @param categoryCode
     * @return com.glodon.qydata.vo.common.ResponseVo<com.glodon.qydata.vo.standard.trade.StandardTradeVO>
     * <AUTHOR>
     * @date 2022/1/17 19:26
     */
    @Operation(summary = "根据一级工程分类 和 工程专业 获取工程特征(指标库-数据分析)--企业级")
    @GetMapping("/getFeatureByCategoryAndTrade")
    public ResponseVo<List<ProjectFeatureResultVO>> getFeatureByCategoryAndTrade(String categoryCode, Long tradeId) {
        return ResponseVo.success(projectFeatureService.getFeatureByCategoryAndTrade(categoryCode, tradeId));
    }

    /**
     * @description: 通过Excel导入内置工程特征数据--企业级 （非业务接口，可废弃）
     * @param file
     * @return com.glodon.qydata.vo.common.ResponseVo
     * <AUTHOR>
     * @date 2022/2/28 10:08
     */
    @Operation(summary = "通过Excel导入内置工程特征数据--企业级")
    @PostMapping("/importTz")
    public ResponseVo importTz(@RequestParam("file") MultipartFile file){
//        if (file.isEmpty()) {
//            return ResponseVo.error();
//        }
//        projectFeatureService.importTz(file);
        return ResponseVo.success();
    }

//------------------------------------------------------以下是个人级别-------------------------------------------------------------------
    /**
     * @description: 新增工程特征--个人级
     * @param featureAddVO
     * @return com.gcj.common.ResponseVo<com.gcj.zblib.standardData.feature.entity.ProjectFeature>
     * <AUTHOR>
     * @date 2021/10/21 19:45
     */
    @Operation(summary = "新增工程特征--个人级")
    @Permission
    @PostMapping
    public ResponseVo<ProjectFeatureResultVO> add(@RequestBody @Validated ProjectFeatureAddVO featureAddVO){
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.FEATURE, globalId, customerCode);

        Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        List<CommonProjCategory> categoryListTree = CategoryUtil.createProjcategoryTree(commonProjCategoryService.getCategoryListTree(customerCode, type, Constants.CategoryConstants.WHETHER_FALSE));
        featureAddVO.setCategoryListTree(categoryListTree);

        Long featureId = projectFeatureSelfService.add(featureAddVO);
        ProjectFeatureResultVO featureResultVO = projectFeatureSelfService.getCompleteFeatureByPrimaryKey(featureId, featureAddVO.getViewType(), featureAddVO.getCategoryCode(), categoryListTree);
        return ResponseVo.success(featureResultVO);
    }

    /**
     * @description: 根据工程特征ID删除工程特征--个人级
     * @param deleteVO
     * @return com.gcj.common.ResponseVo
     * <AUTHOR>
     * @date 2021/10/21 19:51
     */
    @Operation(summary = "根据工程特征ID删除工程特征--个人级")
    @Permission
    @DeleteMapping
    public ResponseVo delete(@Validated ProjectFeatureDeleteVO deleteVO){
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.FEATURE, globalId, customerCode);
        projectFeatureSelfService.delete(deleteVO, customerCode);
        return ResponseVo.success();
    }


    /**
     * @description: 更新工程特征信息--个人级
     * @param updateVO
     * @return com.gcj.common.ResponseVo<com.gcj.zblib.standardData.feature.entity.ProjectFeature>
     * <AUTHOR>
     * @date 2021/10/24 19:47
     */
    @Operation(summary = "更新工程特征信息--个人级")
    @Permission
    @PutMapping
    public ResponseVo<ProjectFeatureResultVO> update(@RequestBody @Validated ProjectFeatureUpdateVO updateVO){
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.FEATURE, globalId, customerCode);

        Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        List<CommonProjCategory> categoryListTree = CategoryUtil.createProjcategoryTree(commonProjCategoryService.getCategoryListTree(customerCode, type, Constants.CategoryConstants.WHETHER_FALSE));
        updateVO.setCategoryListTree(categoryListTree);

        Long featureId = projectFeatureSelfService.update(updateVO);
        ProjectFeatureResultVO featureResultVO = projectFeatureSelfService.getCompleteFeatureByPrimaryKey(featureId, updateVO.getViewType(), updateVO.getCategoryCode(), categoryListTree);
        return ResponseVo.success(featureResultVO);
    }

    /**
     * @description: 上移下移--个人级
     * @param projectFeatureUpOrDownVO
     * @return com.gcj.common.ResponseVo
     * <AUTHOR>
     * @date 2021/10/22 14:11
     */
    @Operation(summary = "上移下移--个人级")
    @Permission
    @PutMapping("/downOrUp")
    public ResponseVo downOrUp(@RequestBody @Validated ProjectFeatureUpOrDownVO projectFeatureUpOrDownVO){
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.FEATURE, globalId, customerCode);
        projectFeatureSelfService.downOrUp(projectFeatureUpOrDownVO);
        return ResponseVo.success();
    }

    /**
     * 快速插入特征到当前分类
     * @param featureTradeVO
     * @return
     */
    @Operation(summary = "快速插入工程特征")
    @Permission
    @PostMapping("/quickInsert")
    public ResponseVo batchInsertFeature(@RequestBody @Validated ProjectFeatureBatchAddVO featureTradeVO) {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.FEATURE, globalId, customerCode);

        Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        List<CommonProjCategory> categoryListTree = CategoryUtil.createProjcategoryTree(commonProjCategoryService.getCategoryListTree(customerCode, type, Constants.CategoryConstants.WHETHER_FALSE));
        featureTradeVO.setCategoryListTree(categoryListTree);

        projectFeatureSelfService.batchInsertFeature(featureTradeVO);
        return ResponseVo.success();
    }

    /**
     * @description: 获取暂存特征列表（分类视图、专业视图）--个人级
     * @param
     * @return
     * <AUTHOR>
     * @date 2022/2/10 12:40
     */
    @Operation(summary = "获取暂存特征列表（分类视图、专业视图）--个人级")
    @Permission
    @GetMapping("/self")
    public ResponseVo getSelfFeatureList(ProjectFeatureFilterVO filterVO){
        String customerCode = getCustomerCode();
        String globalId = getGlobalId();
        publishLockerUtil.lock(OperateConstants.FEATURE, globalId, customerCode);
        filterVO.setIsSkipUserName(WHETHER_FALSE);

        if (Constants.ZbFeatureConstants.ViewType.TRADE_VIEW.equals(filterVO.getViewType()) && Objects.nonNull(filterVO.getTradeId())){
            // 专业视图
            List<ProjectFeatureResultVO> selfFeatureTradeView = projectFeatureSelfService.getSelfFeatureTradeView(customerCode, filterVO);
            return ResponseVo.success(selfFeatureTradeView);
        }

        if (Constants.ZbFeatureConstants.ViewType.CATEGORY_VIEW.equals(filterVO.getViewType()) && StringUtils.isNotEmpty(filterVO.getCategoryCode())){
            // 分类视图   查询某企业的某个分类的特征。根据专业排序显示
            filterVO.setIsShowNotUsing(Constants.ZbFeatureConstants.WHETHER_TRUE);
            List<ProjectFeatureCategoryViewVO> selfFeatureCategoryView = projectFeatureSelfService.getSelfFeatureCategoryView(customerCode, filterVO);
            return ResponseVo.success(selfFeatureCategoryView);
        }

        return ResponseVo.error();
    }

    @Operation(summary = "批量添加计算口径")
    @Permission
    @PostMapping(value = "/batchAddExpressions")
    public ResponseVo<String> batchAddExpressions(@RequestBody @Validated BatchAddExpressionDto batchAddExpressionDto) {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        try {
            publishLockerUtil.lock(OperateConstants.FEATURE, globalId, customerCode);
            long l = System.currentTimeMillis();
            projectFeatureService.batchAddExpressions(batchAddExpressionDto, globalId, customerCode);
            log.info("批量添加口径耗时：{}ms", System.currentTimeMillis() - l);
            return ResponseVo.success();
        } finally {
            publishLockerUtil.unLock(OperateConstants.FEATURE, globalId, customerCode);
        }
    }

    /**
     * 发布数据--个人级
     */
    @Operation(summary = "发布--个人级")
    @Permission
    @PostMapping("/publish")
    public ResponseVo publish(){
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.FEATURE, globalId, customerCode);
        long l = System.currentTimeMillis();
        projectFeatureSelfService.publish(customerCode, globalId);
        log.info("发布接口耗时：{}ms", System.currentTimeMillis() - l);

        publishLockerUtil.unLock(OperateConstants.FEATURE, getGlobalId(), customerCode);
        return ResponseVo.success();
    }

    /**
     * 刷ord（非业务接口，可废弃）
     */
    @Operation(summary = "刷ord")
    @GetMapping("/tempRefreshOrd")
    public ResponseVo tempRefreshOrd(String customerCode){
        projectFeatureService.tempRefreshOrd(customerCode);
        return ResponseVo.success();
    }
}
