package com.glodon.qydata.util;

import com.glodon.qydata.common.enums.PublishTypeEnum;
import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.service.system.IGlodonUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static com.glodon.qydata.common.constant.Constants.SYSTEM_ADMIN;

/**
 * @author: luoml-b
 * @date: 2024/4/17 13:40
 * @description: 暂存数据发布所
 */
@Component
@Slf4j
public class PublishLockerUtil {

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private IGlodonUserService glodonUserService;

    public boolean verifyLockEnt(String type, String customerCode) {
        String lockGlobalId = redisUtil.getString(RedisKeyEnum.PUBLISH_LOCK, type, customerCode);
        return StringUtils.isNotBlank(lockGlobalId);
    }

    public void verifyLockUser(String type, String globalId, String customerCode) {
        String lockGlobalId = redisUtil.getString(RedisKeyEnum.PUBLISH_LOCK, type, customerCode);
        //如果当前企业已经有用户在修订，需要判断是不是自己，是自己则放过，不是自己则返回锁住人的名称
        if (StringUtils.isNotBlank(lockGlobalId)) {
            if (lockGlobalId.equals(globalId)) {
                return;
            }
            if (Objects.equals(lockGlobalId, SYSTEM_ADMIN)){
                throw new BusinessException(ResponseCode.EDIT_FORBIDDEN, PublishTypeEnum.getNameByCode(type) + "数据升级中，请稍后再试");
            }
            String userName = glodonUserService.getUserName(lockGlobalId, null);
            throw new BusinessException(ResponseCode.EDIT_FORBIDDEN, userName
                    + "正在编辑" + PublishTypeEnum.getNameByCode(type) + "，请稍后再试");
        }
        //如果当前企业没有锁，则提示刷新页面
        throw new BusinessException(ResponseCode.EDIT_FORBIDDEN, "请刷新页面之后再修订");

    }

    public void lock(String type, String globalId, String customerCode) {
        String lockGlobalId = redisUtil.getString(RedisKeyEnum.PUBLISH_LOCK, type, customerCode);
        //如果当前企业已经有用户在修订，需要判断是不是自己，是自己则续期，不是自己则返回锁住人的名称
        if (StringUtils.isNotBlank(lockGlobalId)) {
            if (lockGlobalId.equals(globalId)) {
                renewalLock(type, globalId, customerCode);
                return;
            }
            if (Objects.equals(lockGlobalId, SYSTEM_ADMIN)){
                throw new BusinessException(ResponseCode.EDIT_FORBIDDEN, PublishTypeEnum.getNameByCode(type) + "数据升级中，请稍后再试");
            }
            String userName = glodonUserService.getUserName(lockGlobalId, null);
            throw new BusinessException(ResponseCode.EDIT_FORBIDDEN, userName
                    + "正在编辑" + PublishTypeEnum.getNameByCode(type) + "，请稍后再试");
        }
        //当前企业没有用户在修订，使用setnx加锁保证原子性，如果同步抢锁有一个失败，需要查出谁拿到锁返回前端
        if (!redisUtil.tryLock(globalId, RedisKeyEnum.PUBLISH_LOCK, type, customerCode)) {
            String syncLockGlobalId = redisUtil.getString(RedisKeyEnum.PUBLISH_LOCK, type, customerCode);
            if (Objects.equals(syncLockGlobalId, SYSTEM_ADMIN)){
                throw new BusinessException(ResponseCode.EDIT_FORBIDDEN, PublishTypeEnum.getNameByCode(type) + "数据升级中，请稍后再试");
            }
            String userName = glodonUserService.getUserName(syncLockGlobalId, null);
            throw new BusinessException(ResponseCode.EDIT_FORBIDDEN, userName
                    + "正在编辑" + PublishTypeEnum.getNameByCode(type) + "，请稍后再试");
        }
    }

    public void unLock(String type, String globalId, String customerCode) {
        redisUtil.unlock(globalId, RedisKeyEnum.PUBLISH_LOCK, type, customerCode);
    }

    public void renewalLock(String type, String globalId, String customerCode) {
        String lockGlobalId = redisUtil.getString(RedisKeyEnum.PUBLISH_LOCK, type, customerCode);
        if (globalId.equals(lockGlobalId)) {
            redisUtil.setString(RedisKeyEnum.PUBLISH_LOCK, lockGlobalId, type, customerCode);
        }
    }
}
