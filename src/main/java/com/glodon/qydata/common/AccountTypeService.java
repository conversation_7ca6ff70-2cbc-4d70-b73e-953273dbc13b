package com.glodon.qydata.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.glodon.qydata.common.enums.AccountTypeEnum;
import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.service.system.IGlodonUserService;
import com.glodon.qydata.util.RedisUtil;
import com.glodon.qydata.util.SpringUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * @packageName: com.glodon.qydata.common
 * @className: AccountTypeService
 * @author: yanyuhui <EMAIL>
 * @date: 2025-07-28 18:23
 * @description:
 */
@Service
public class AccountTypeService {

    @Resource
    private RedisUtil redisUtil;

    /**
     * 获取当前账号类型
     *
     * @param globalId 用户id
     * @return 账号类型
     */
    public AccountTypeEnum getAccountType(String globalId) {
        String ret = redisUtil.getString(RedisKeyEnum.ACCOUNT_TYPE_KEY, globalId);
        if (ret != null) {
            return EnumUtil.getBy(AccountTypeEnum.class, (x) -> x.getValue().equals(ret));
        }
        IGlodonUserService glodonUserService = SpringUtil.getBean(IGlodonUserService.class);
        JSONObject json = glodonUserService.getEntInforFromCached(globalId, StrUtil.EMPTY);
        if (CollUtil.isNotEmpty(json) && CollUtil.isNotEmpty(json.getJSONObject("enterprise"))) {
            redisUtil.setString(RedisKeyEnum.ACCOUNT_TYPE_KEY, AccountTypeEnum.ENTERPRISE_TYPE.getValue(), globalId);
            return AccountTypeEnum.ENTERPRISE_TYPE;
        }
        //redisUtil.setString(RedisKeyEnum.ACCOUNT_TYPE_KEY, AccountTypeEnum.PERSONAL_TYPE.getValue(), globalId);
        return AccountTypeEnum.PERSONAL_TYPE;
    }

}
