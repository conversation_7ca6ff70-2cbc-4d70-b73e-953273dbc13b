package com.glodon.qydata.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @packageName: com.glodon.qydata.common.enums
 * @className: AccountTypeEnum
 * @author: yany<PERSON><PERSON> <EMAIL>
 * @date: 2025-07-28 18:15
 * @description:
 */
@AllArgsConstructor
@Getter
public enum AccountTypeEnum {
    ENTERPRISE_TYPE("enterprise_account", "企业级账号"),
    PERSONAL_TYPE("personal_account", "企业级账号"),
    ;
    private final String value;
    private final String desc;

}
